import { 
  N8nNode, 
  WebhookNodeParams, 
  HttpRequestNodeParams, 
  SetNodeParams, 
  SwitchNodeParams,
  GoogleDocsNodeParams,
  TelegramNodeParams,
  WaitNodeParams,
  AIAgentNodeParams
} from '../types/workflow';

export class NodeFactory {
  
  static createWebhookTrigger(
    id: string, 
    name: string, 
    position: [number, number],
    params: Partial<WebhookNodeParams> = {}
  ): N8nNode {
    return {
      id,
      name,
      type: 'n8n-nodes-base.webhook',
      typeVersion: 1,
      position,
      parameters: {
        httpMethod: 'POST',
        path: 'tabu-inbound',
        responseMode: 'onReceived',
        ...params
      }
    };
  }

  static createSetNode(
    id: string,
    name: string,
    position: [number, number],
    values: Array<{ name: string; value: any; type?: string }> = []
  ): N8nNode {
    return {
      id,
      name,
      type: 'n8n-nodes-base.set',
      typeVersion: 3,
      position,
      parameters: {
        values: {
          values: values
        },
        options: {
          dotNotation: true
        }
      }
    };
  }

  static createHttpRequest(
    id: string,
    name: string,
    position: [number, number],
    params: Partial<HttpRequestNodeParams>
  ): N8nNode {
    const baseParams = {
      method: 'GET' as const,
      url: '',
      ...params
    };

    // Remove undefined/empty options to prevent import errors
    const cleanParams = Object.fromEntries(
      Object.entries(baseParams).filter(([_, value]) =>
        value !== undefined && value !== null &&
        !(typeof value === 'object' && Object.keys(value).length === 0)
      )
    );

    return {
      id,
      name,
      type: 'n8n-nodes-base.httprequest',
      typeVersion: 4,
      position,
      parameters: cleanParams
    };
  }

  static createSwitchNode(
    id: string,
    name: string,
    position: [number, number],
    rules: Array<{ operation: string; value1: any; value2?: any; output: number }> = []
  ): N8nNode {
    return {
      id,
      name,
      type: 'n8n-nodes-base.switch',
      typeVersion: 3,
      position,
      parameters: {
        mode: 'rules',
        rules: {
          rules: rules.length > 0 ? rules : [
            {
              operation: 'contains',
              value1: '={{$json.message_text}}',
              value2: 'human',
              output: 0
            }
          ]
        },
        fallbackOutput: rules.length > 0 ? rules.length : 1
      }
    };
  }

  static createGoogleDocsNode(
    id: string,
    name: string,
    position: [number, number],
    params: Partial<GoogleDocsNodeParams> = {}
  ): N8nNode {
    return {
      id,
      name,
      type: 'n8n-nodes-base.googledocs',
      typeVersion: 2,
      position,
      parameters: {
        operation: 'get',
        ...params
      },
      credentials: {
        googleApi: 'google-service-account'
      }
    };
  }

  static createAIAgentNode(
    id: string,
    name: string,
    position: [number, number],
    params: Partial<AIAgentNodeParams> = {}
  ): N8nNode {
    return {
      id,
      name,
      type: 'n8n-nodes-langchain.agent',
      typeVersion: 1,
      position,
      parameters: {
        sessionId: '={{$json.contact_id}}',
        chatInput: '={{$json.message_text}}',
        options: {
          returnIntermediateSteps: false
        },
        ...params
      }
    };
  }

  static createTelegramNode(
    id: string,
    name: string,
    position: [number, number],
    params: Partial<TelegramNodeParams>
  ): N8nNode {
    return {
      id,
      name,
      type: 'n8n-nodes-base.telegram',
      typeVersion: 1,
      position,
      parameters: {
        operation: 'sendMessage',
        ...params
      },
      credentials: {
        telegramApi: 'telegram-bot'
      }
    };
  }

  static createWaitNode(
    id: string,
    name: string,
    position: [number, number],
    amount: number = 12,
    unit: 'seconds' | 'minutes' | 'hours' | 'days' = 'hours'
  ): N8nNode {
    return {
      id,
      name,
      type: 'n8n-nodes-base.wait',
      typeVersion: 1,
      position,
      parameters: {
        amount,
        unit
      }
    };
  }
}
