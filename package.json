{"name": "tabu-automation", "version": "1.0.0", "description": "TypeScript-based n8n workflow generator for Tabu Enterprise AI Receptionist", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "generate": "npm run build && npm run start", "watch": "nodemon --exec ts-node src/index.ts"}, "keywords": ["n8n", "workflow", "automation", "typescript", "tabu"], "author": "Tabu Enterprise", "license": "ISC", "devDependencies": {"@types/node": "^20.19.4", "nodemon": "^3.0.0", "ts-node": "^10.0.0", "typescript": "^5.8.3"}}