## 🧑‍💻 TypeScript Coding Guidelines for n8n Workflow Automation

### 🎯 Project Goal and Output

**Primary Objective**
- Use TypeScript to programmatically generate a single, comprehensive JSON workflow file
- Output file name: `tabu-workflow.json`
- This JSON file contains the complete n8n workflow definition for the Tabu AI receptionist automation
- Manual upload approach: Generate the JSON file locally, then manually import it into n8n Cloud interface
- No automatic deployment or real-time API integration with n8n platform required

**Development Workflow**
- Write TypeScript code to build the workflow structure programmatically
- Generate and update the `tabu-workflow.json` file through TypeScript compilation/execution
- Manually upload the generated JSON file to n8n Cloud through the web interface
- Test and iterate by regenerating the JSON file and re-uploading as needed
- Keep the development process simple and focused on JSON generation, not platform integration

### Core Development Principles

**Use TypeScript for JSON Generation**
- Always use TypeScript instead of plain JavaScript for workflow JSON generation
- Leverage strong typing to prevent errors in workflow structure and node configurations
- Create reusable interfaces for workflow components (nodes, connections, credentials)
- Focus on generating clean, valid JSON output that can be imported into n8n Cloud
- Implement validation to ensure the generated JSON matches n8n's expected workflow format

**Modular JSON Generation Architecture**
- Build separate TypeScript modules for different workflow components (triggers, actions, logic nodes)
- Create configuration objects that define workflow behavior and can be easily modified
- Design workflow generators as classes that output complete JSON workflow definitions
- Implement factory patterns for creating different types of nodes that compile to JSON
- Keep the final output as a single `tabu-workflow.json` file for easy manual upload

**Version Control and Documentation**
- Store all TypeScript workflow generation code in Git repositories with proper branching strategies
- Document all node parameter configurations and their business logic purposes
- Version control the generated `tabu-workflow.json` file alongside the TypeScript source code
- Maintain changelog files for tracking workflow JSON modifications and manual upload history
- Use meaningful commit messages that describe business impact and JSON structure changes

### n8n-Specific Development Guidelines

**Workflow Structure Standards**
- Always define clear naming conventions for workflow names, node names, and variable names
- Use descriptive node names that reflect business functions (e.g., "🔔 Listen to Inbound" instead of "Webhook1")
- Implement consistent positioning coordinates for nodes to maintain visual organization
- Group related nodes logically and maintain consistent spacing between workflow sections

**Node Configuration Best Practices**
- Research available node types through the n8n REST API `/rest/node-types` endpoint before building
- Always specify the correct typeVersion for each node to ensure compatibility
- Use expressions and variables consistently across similar workflow implementations
- Implement proper credential management with environment-specific configurations

**JSON Structure Standards**
- Study n8n workflow JSON format by exporting existing workflows from n8n Cloud interface
- Ensure generated JSON includes all required fields: name, nodes, connections, active status, settings
- Validate JSON structure against n8n's expected format before manual upload
- Include proper node IDs, positions, and parameter configurations in the generated JSON
- Test JSON validity by importing sample workflows and examining their structure

**Data Flow and Logic Design**
- Map out data flow between nodes before implementing the workflow structure
- Use consistent data transformation patterns across similar business logic
- Implement proper error handling paths for all critical workflow branches
- Design fallback mechanisms for when external services are unavailable

### Development Workflow Guidelines

**JSON Generation and Upload Process**
- Develop and test TypeScript code locally to generate `tabu-workflow.json`
- Use development n8n instance for testing generated JSON workflows before production
- Create backup copies of working `tabu-workflow.json` files before making changes
- Manually upload updated JSON files to n8n Cloud through the web interface import feature
- Keep the process simple: TypeScript → JSON generation → Manual upload → Test → Iterate

**Testing and Quality Assurance**
- Build unit tests for TypeScript workflow generation logic and JSON output validation
- Test generated JSON by manually importing into development n8n instance
- Validate all workflow paths including error scenarios and edge cases after upload
- Verify webhook endpoints and external API integrations work correctly in uploaded workflow
- Test the complete manual upload and activation process before production deployment

**Client Customization Strategy**
- Create base TypeScript templates that generate customized `tabu-workflow.json` files per client
- Build configuration objects where business rules can be modified to generate different JSON outputs
- Implement feature flags in TypeScript that conditionally include workflow components in generated JSON
- Design the TypeScript generator to produce client-specific JSON files that can be manually uploaded
- Maintain separate configuration files for different clients while using the same TypeScript generation logic

### Essential Documentation and Resources

**Official n8n Documentation**
- n8n REST API Documentation: https://docs.n8n.io/api/
- Built-in Node Reference: https://docs.n8n.io/integrations/builtin/
- Core Nodes Documentation: https://docs.n8n.io/integrations/builtin/core-nodes/
- Workflow Development Guide: https://docs.n8n.io/workflows/

**Node Types and Configuration References**
- HTTP Request Node Documentation: https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/
- Webhook Node Documentation: https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.webhook/
- Code Node Documentation: https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.code/
- Switch Node Documentation: https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.switch/

**API Integration Resources**
- n8n API Reference: https://docs.n8n.io/api/api-reference/
- Authentication Guide: https://docs.n8n.io/api/authentication/
- Pagination Handling: https://docs.n8n.io/api/pagination/
- API Playground Usage: https://docs.n8n.io/api/using-api-playground/

**Community and Learning Resources**
- n8n Community Forum: https://community.n8n.io/
- Workflow Templates Library: https://n8n.io/workflows/
- Node Creation Tutorials: https://docs.n8n.io/integrations/creating-nodes/
- TypeScript Best Practices: https://docs.n8n.io/integrations/creating-nodes/build/programmatic-style-node/

**External API Documentation for Tabu Project**
- Respond.io API Documentation: https://developers.respond.io/
- OpenRouter AI API Documentation: https://openrouter.ai/docs
- Google Docs API Reference: https://developers.google.com/docs/api
- Telegram Bot API: https://core.telegram.org/bots/api

### Productivity and Maintenance Guidelines

**Code Organization Standards**
- Create separate TypeScript files for different workflow components
- Use barrel exports to organize and expose workflow generation functions
- Implement consistent error handling patterns across all workflow generators
- Maintain clear separation between configuration data and business logic

**JSON Generation and Manual Upload Process**
- Build TypeScript scripts that generate the `tabu-workflow.json` file automatically
- Create simple build processes that compile TypeScript and output the final JSON file
- Document the manual upload process for importing JSON files into n8n Cloud
- Establish procedures for backing up current workflows before uploading new versions
- Create checklists for validating JSON structure before manual upload to prevent import errors

**Scalability Considerations**
- Design workflow generators to handle multiple client configurations simultaneously
- Implement caching strategies for frequently accessed configuration data
- Build workflow templates that can be easily extended with new features
- Plan for horizontal scaling of workflow execution across multiple n8n instances

**Security and Compliance**
- Implement proper credential management and rotation procedures
- Use environment variables for all sensitive configuration data
- Audit workflow access permissions and API key usage regularly
- Maintain compliance with data protection regulations in workflow design
