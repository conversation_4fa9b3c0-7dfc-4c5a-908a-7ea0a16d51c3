// n8n Workflow Type Definitions

export interface N8nWorkflow {
  name: string;
  nodes: N8nNode[];
  connections: N8nConnections;
  active: boolean;
  settings: WorkflowSettings;
  staticData?: Record<string, any>;
  meta?: WorkflowMeta;
  pinData?: Record<string, any>;
  versionId?: string;
}

export interface N8nNode {
  id: string;
  name: string;
  type: string;
  typeVersion: number;
  position: [number, number];
  parameters: Record<string, any>;
  credentials?: Record<string, string>;
  webhookId?: string;
  disabled?: boolean;
  notes?: string;
  notesInFlow?: boolean;
  color?: string;
  continueOnFail?: boolean;
  alwaysOutputData?: boolean;
  executeOnce?: boolean;
  retryOnFail?: boolean;
  maxTries?: number;
  waitBetweenTries?: number;
  onError?: 'stopWorkflow' | 'continueRegularOutput' | 'continueErrorOutput';
}

export interface N8nConnections {
  [sourceNodeName: string]: {
    [outputType: string]: Array<Array<{
      node: string;
      type: string;
      index: number;
    }>>;
  };
}

export interface WorkflowSettings {
  executionOrder?: 'v0' | 'v1';
  saveManualExecutions?: boolean;
  callerPolicy?: 'workflowsFromSameOwner' | 'workflowsFromAList' | 'any';
  callerIds?: string;
  errorWorkflow?: string;
  timezone?: string;
  saveExecutionProgress?: boolean;
  saveDataErrorExecution?: 'all' | 'none';
  saveDataSuccessExecution?: 'all' | 'none';
  executionTimeout?: number;
}

export interface WorkflowMeta {
  templateCredsSetupCompleted?: boolean;
  instanceId?: string;
}

// Node-specific parameter interfaces
export interface WebhookNodeParams {
  httpMethod: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS';
  path: string;
  responseMode: 'onReceived' | 'lastNode' | 'responseNode';
  responseData?: string;
  responseBinaryPropertyName?: string;
  responseContentType?: string;
  responseHeaders?: Record<string, string>;
  options?: {
    allowedOrigins?: string;
    rawBody?: boolean;
    noResponseBody?: boolean;
  };
}

export interface HttpRequestNodeParams {
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS';
  sendQuery?: boolean;
  queryParameters?: {
    parameters: Array<{
      name: string;
      value: string;
    }>;
  };
  sendHeaders?: boolean;
  headerParameters?: {
    parameters: Array<{
      name: string;
      value: string;
    }>;
  };
  sendBody?: boolean;
  bodyParameters?: {
    parameters: Array<{
      name: string;
      value: string;
    }>;
  };
  jsonParameters?: string;
  options?: {
    timeout?: number;
    response?: {
      response?: {
        responseFormat?: 'json' | 'text' | 'file';
        outputPropertyName?: string;
      };
    };
    redirect?: {
      redirect?: {
        followRedirects?: boolean;
        maxRedirects?: number;
      };
    };
  };
}

export interface SetNodeParams {
  values: {
    values: Array<{
      name: string;
      value: any;
      type?: 'string' | 'number' | 'boolean' | 'array' | 'object';
    }>;
  };
  options?: {
    dotNotation?: boolean;
    ignoreConversionErrors?: boolean;
  };
}

export interface SwitchNodeParams {
  mode: 'rules' | 'expression';
  rules?: {
    rules: Array<{
      operation: string;
      value1: any;
      value2?: any;
      output: number;
    }>;
  };
  expression?: string;
  fallbackOutput?: number;
}

export interface GoogleDocsNodeParams {
  operation: 'get' | 'create' | 'update';
  documentId?: string;
  title?: string;
  folderId?: string;
  updateFields?: Record<string, any>;
  options?: Record<string, any>;
}

export interface TelegramNodeParams {
  operation: 'sendMessage' | 'sendPhoto' | 'sendDocument' | 'editMessage' | 'deleteMessage';
  chatId: string;
  text?: string;
  parseMode?: 'HTML' | 'Markdown' | 'MarkdownV2';
  disableWebPagePreview?: boolean;
  disableNotification?: boolean;
  replyToMessageId?: number;
  replyMarkup?: Record<string, any>;
  additionalFields?: Record<string, any>;
}

export interface WaitNodeParams {
  amount: number;
  unit: 'seconds' | 'minutes' | 'hours' | 'days';
  resume?: 'webhook' | 'form';
}

export interface AIAgentNodeParams {
  sessionId?: string;
  chatInput?: string;
  options?: {
    returnIntermediateSteps?: boolean;
    promptValues?: Record<string, any>;
  };
}
