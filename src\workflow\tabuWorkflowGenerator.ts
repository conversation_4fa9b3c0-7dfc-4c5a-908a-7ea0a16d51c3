import { N8nWorkflow, N8nConnections } from '../types/workflow';
import { NodeFactory } from '../nodes/nodeFactory';

export class TabuWorkflowGenerator {
  
  generateWorkflow(): N8nWorkflow {
    const nodes = [
      // 1. Webhook Trigger
      NodeFactory.createWebhookTrigger(
        'webhook-trigger',
        '🔔 Listen to Inbound',
        [250, 300],
        {
          path: 'tabu-inbound',
          httpMethod: 'POST',
          responseMode: 'onReceived'
        }
      ),

      // 2. Extract Data
      NodeFactory.createSetNode(
        'extract-data',
        '📋 Extract Data',
        [450, 300],
        [
          { name: 'message_text', value: '={{$json.body.message.text || $json.message || ""}}' },
          { name: 'contact_phone', value: '={{$json.body.contact.phone || $json.phone || ""}}' },
          { name: 'contact_email', value: '={{$json.body.contact.email || $json.email || ""}}' },
          { name: 'channel_type', value: '={{$json.body.channel.type || "unknown"}}' },
          { name: 'contact_id', value: '={{$json.body.contact.id || ""}}' }
        ]
      ),

      // 3. Get Contact from Respond.io
      NodeFactory.createHttpRequest(
        'get-contact',
        '👤 Get Contact',
        [650, 300],
        {
          method: 'GET',
          url: 'https://api.respond.io/v2/contacts',
          sendQuery: true,
          queryParameters: {
            parameters: [
              { name: 'phone', value: '={{$json.contact_phone}}' }
            ]
          },
          sendHeaders: true,
          headerParameters: {
            parameters: [
              { name: 'Authorization', value: 'Bearer YOUR_RESPOND_IO_API_KEY' },
              { name: 'Content-Type', value: 'application/json' }
            ]
          }
        }
      ),

      // 4. Route Messages (Switch Node)
      NodeFactory.createSwitchNode(
        'route-messages',
        '🧭 Route Messages',
        [850, 300],
        [
          {
            operation: 'regex',
            value1: '={{$json.message_text}}',
            value2: '(human|manager|real person|talk to|someone|agent|representative)',
            output: 0
          },
          {
            operation: 'isEmpty',
            value1: '={{$json.message_text}}',
            output: 1
          }
        ]
      ),

      // 5. Google Docs Knowledge Base
      NodeFactory.createGoogleDocsNode(
        'google-docs',
        '📄 Get Knowledge Base',
        [1050, 200],
        {
          operation: 'get',
          documentId: 'YOUR_GOOGLE_DOC_ID'
        }
      ),

      // 6. Prepare AI Context
      NodeFactory.createSetNode(
        'prepare-ai-context',
        '🤖 Prepare AI Context',
        [1250, 200],
        [
          { name: 'restaurant_info', value: '={{$json.body.content}}' },
          { name: 'customer_message', value: '={{$("route-messages").item.json.message_text}}' },
          { name: 'ai_prompt', value: `You are Tabu Enterprise's AI receptionist. Use this restaurant information: {{$json.restaurant_info}}

Customer message: {{$json.customer_message}}

Respond professionally and helpfully. If you cannot answer, suggest they speak with a human agent.` }
        ]
      ),

      // 7. AI Agent Processing
      NodeFactory.createAIAgentNode(
        'ai-agent',
        '🧠 AI Agent',
        [1450, 200],
        {
          sessionId: '={{$("get-contact").item.json.id}}',
          chatInput: '={{$json.ai_prompt}}'
        }
      ),

      // 8. Parse AI Response
      NodeFactory.createSetNode(
        'parse-ai-response',
        '✂️ Parse AI Response',
        [1650, 200],
        [
          { name: 'ai_reply', value: '={{$json.output}}' },
          { name: 'contact_id', value: '={{$("get-contact").item.json.id}}' },
          { name: 'formatted_message', value: '={{$json.ai_reply}}' }
        ]
      ),

      // 9. Send AI Reply
      NodeFactory.createHttpRequest(
        'send-ai-reply',
        '💬 Send AI Reply',
        [1850, 200],
        {
          method: 'POST',
          url: 'https://api.respond.io/v2/contacts/{{$json.contact_id}}/messages',
          sendHeaders: true,
          headerParameters: {
            parameters: [
              { name: 'Authorization', value: 'Bearer YOUR_RESPOND_IO_API_KEY' },
              { name: 'Content-Type', value: 'application/json' }
            ]
          },
          sendBody: true,
          jsonParameters: JSON.stringify({
            message: {
              type: 'text',
              text: '={{$json.formatted_message}}'
            }
          })
        }
      ),

      // 10. Add Human Tag (Escalation Path)
      NodeFactory.createSetNode(
        'add-human-tag',
        '🏷️ Add Human Tag',
        [1050, 400],
        [
          { name: 'contact_id', value: '={{$("get-contact").item.json.id}}' },
          { name: 'tag_data', value: { tags: ['human_needed'] } }
        ]
      ),

      // 11. Tag Contact
      NodeFactory.createHttpRequest(
        'tag-contact',
        '🚨 Tag Contact',
        [1250, 400],
        {
          method: 'POST',
          url: 'https://api.respond.io/v2/contacts/{{$json.contact_id}}/tags',
          sendHeaders: true,
          headerParameters: {
            parameters: [
              { name: 'Authorization', value: 'Bearer YOUR_RESPOND_IO_API_KEY' },
              { name: 'Content-Type', value: 'application/json' }
            ]
          },
          sendBody: true,
          jsonParameters: '={{JSON.stringify($json.tag_data)}}'
        }
      ),

      // 12. Send Escalation Notification via Telegram
      NodeFactory.createTelegramNode(
        'telegram-notification',
        '📞 Telegram Notification',
        [1450, 400],
        {
          operation: 'sendMessage',
          chatId: 'YOUR_TELEGRAM_CHAT_ID',
          text: `🚨 Human escalation requested!

Customer: {{$("get-contact").item.json.name || "Unknown"}}
Phone: {{$("extract-data").item.json.contact_phone}}
Message: {{$("extract-data").item.json.message_text}}

Please respond in Respond.io platform.`,
          parseMode: 'HTML'
        }
      ),

      // 13. Wait 12 Hours
      NodeFactory.createWaitNode(
        'wait-12-hours',
        '⏰ Wait 12 Hours',
        [1650, 400],
        12,
        'hours'
      ),

      // 14. Remove Tag
      NodeFactory.createHttpRequest(
        'remove-tag',
        '🔄 Remove Tag',
        [1850, 400],
        {
          method: 'DELETE',
          url: 'https://api.respond.io/v2/contacts/{{$("tag-contact").item.json.contact_id}}/tags/human_needed',
          sendHeaders: true,
          headerParameters: {
            parameters: [
              { name: 'Authorization', value: 'Bearer YOUR_RESPOND_IO_API_KEY' }
            ]
          }
        }
      ),

      // 15. Fallback Message
      NodeFactory.createHttpRequest(
        'fallback-message',
        '🛡️ Fallback Message',
        [1850, 500],
        {
          method: 'POST',
          url: 'https://api.respond.io/v2/contacts/{{$("get-contact").item.json.id}}/messages',
          sendHeaders: true,
          headerParameters: {
            parameters: [
              { name: 'Authorization', value: 'Bearer YOUR_RESPOND_IO_API_KEY' },
              { name: 'Content-Type', value: 'application/json' }
            ]
          },
          sendBody: true,
          jsonParameters: JSON.stringify({
            message: {
              type: 'text',
              text: 'Sorry, I\'m experiencing technical difficulties. Please try again later or contact us directly for assistance.'
            }
          })
        }
      )
    ];

    const connections = this.createConnections();

    return {
      name: 'Tabu Enterprise AI Receptionist',
      nodes,
      connections,
      active: false,
      settings: {
        executionOrder: 'v1',
        saveManualExecutions: true,
        saveExecutionProgress: false,
        saveDataErrorExecution: 'all',
        saveDataSuccessExecution: 'all'
      },
      staticData: {},
      meta: {
        templateCredsSetupCompleted: false
      }
    };
  }

  private createConnections(): N8nConnections {
    return {
      'webhook-trigger': {
        main: [
          [{ node: 'extract-data', type: 'main', index: 0 }]
        ]
      },
      'extract-data': {
        main: [
          [{ node: 'get-contact', type: 'main', index: 0 }]
        ]
      },
      'get-contact': {
        main: [
          [{ node: 'route-messages', type: 'main', index: 0 }]
        ]
      },
      'route-messages': {
        main: [
          [{ node: 'add-human-tag', type: 'main', index: 0 }], // Escalation path
          [{ node: 'fallback-message', type: 'main', index: 0 }], // Empty message fallback
          [{ node: 'google-docs', type: 'main', index: 0 }] // Normal AI path
        ]
      },
      'google-docs': {
        main: [
          [{ node: 'prepare-ai-context', type: 'main', index: 0 }]
        ]
      },
      'prepare-ai-context': {
        main: [
          [{ node: 'ai-agent', type: 'main', index: 0 }]
        ]
      },
      'ai-agent': {
        main: [
          [{ node: 'parse-ai-response', type: 'main', index: 0 }]
        ]
      },
      'parse-ai-response': {
        main: [
          [{ node: 'send-ai-reply', type: 'main', index: 0 }]
        ]
      },
      'add-human-tag': {
        main: [
          [{ node: 'tag-contact', type: 'main', index: 0 }]
        ]
      },
      'tag-contact': {
        main: [
          [{ node: 'telegram-notification', type: 'main', index: 0 }]
        ]
      },
      'telegram-notification': {
        main: [
          [{ node: 'wait-12-hours', type: 'main', index: 0 }]
        ]
      },
      'wait-12-hours': {
        main: [
          [{ node: 'remove-tag', type: 'main', index: 0 }]
        ]
      }
    };
  }
}
