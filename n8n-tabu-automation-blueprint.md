Tabu Enterprise Automation Using n8n

This automation project, titled "Tabu Enterprise Test", is designed to build a virtual AI receptionist for Tabu Bali, a high-end restaurant and nightlife venue in Uluwatu, Bali. The main goal is to automatically respond to guest messages, drive bookings and event attendance, and tag contacts who request a human.

Note: This project uses a combination of Webhook nodes to listen for inbound messages from Respond.io, HTTP Request nodes to communicate with OpenRouter AI, Google Docs, and Respond.io, and standard n8n logic blocks like IF, Set, Switch, and others to control flow logic.

Best Practices: The entire workflow is structured to be clean, well-commented, and easily duplicable for other clients or businesses. Variable names, message templates, and logic paths are modular to support scalability and reuse. It also supports AI agent memory, external knowledge base via Google Docs, multi-channel support including Telegram, and robust human takeover fallback routes.

Core Functional Steps

🔔 Listen to Inbound (Webhook Trigger)

The automation begins when a guest sends a message via WhatsApp, Telegram, or Web Chat. A Webhook node catches this inbound message and extracts both the message content and contact data.

🧾 Get Contact ID

To enable targeted messaging, the system uses either the sender’s phone number or email address to retrieve the Respond.io contact ID via an HTTP Request. This ensures replies are sent to the correct user.

🧭 Basic Router: Decide Which Path to Take

The message is passed through a Router node that decides the direction of the flow:

If it contains escalation keywords (e.g. “human”), it triggers an escalation.

Otherwise, it proceeds through the AI-reply path.

🤖 AI‑Powered Reply with Memory & Knowledgebase (Default Route)

Send to OpenRouter AI (With Memory)

An HTTP POST is sent to OpenRouter’s /chat/completions endpoint using gpt-3.5-turbo-0613 or similar model.

The AI includes memory context via previous interaction logs (stored in a lightweight store or passed as part of the conversation history).

The AI is instructed to behave like a real, local front desk staff:

Answer common questions clearly.

Provide relevant links (booking, events, maps).

Maintain a helpful and human tone.

Inject Google Docs Knowledgebase

Optionally, information fetched from a linked Google Docs document can be included in the AI's prompt context.

This allows the bot to answer from updated information, menus, events, or FAQs stored externally in Docs.

Parse JSON Response

The OpenRouter response is parsed to extract the AI’s actual reply from choices[0].message.content.

Send Message Confirmation or Fallback

The AI’s reply is sent back to the original contact using Respond.io’s HTTP API.

If the AI fails or returns an incomplete message, a fallback message is sent such as:

"Thanks for reaching out! We’re checking your question and will get back shortly — you can also view our reservation options here: https://tabubali.com/book/"

🚨 Escalation Path: Trigger Human Takeover

If the message contains escalation intent like “human,” “manager,” “real person,” “talk to,” or “someone,” the following steps are taken:

Add a Tag

The contact is tagged with human_needed using the Respond.io API.

This tag triggers the Respond.io internal workflow for agent assignment.

Trigger Human Takeover Workflow

Inside Respond.io:

Trigger: Contact is tagged with human_needed

Action: Assign to live agent / send Slack alert / open chat window

Human Takeover Tag Reset (Optional Logic)

A delay of 12 hours is started using n8n delay node.

If no user message within 12 hours, the human_needed tag is removed automatically, allowing future messages to go back to the bot.

🧠 Optional Enhancements

Memory Utility: Messages and replies can be stored to build minimal memory context for the AI (e.g. last 3 messages, booking attempts, FAQs asked).

Telegram Integration: Add Telegram bot webhook listener and route those messages through the same flow, dynamically identifying the channel.

Google Docs Sync: Periodically fetch and cache content from Google Docs (menus, FAQ, events) and inject it into the AI prompt to provide up-to-date answers.

📡 Respond.io API Reference (for HTTP Request Nodes)

These are the critical API endpoints used in the automation flow when communicating with Respond.io:

🔐 Auth Headers (Used in All Calls)

{
  "Authorization": "Bearer YOUR_API_KEY",
  "Content-Type": "application/json"
}

📥 1. Get Contact by Phone or Email

GET https://api.respond.io/v2/contacts?phone=+1234567890

🏷️ 2. Add a Tag to a Contact (e.g. for human takeover)

POST https://api.respond.io/v2/contacts/{CONTACT_ID}/tags

Body:
{
  "tags": ["human_needed"]
}

🧪 Final Testing Checklist

✅ Test Webhook trigger (WhatsApp, Telegram, Web chat)

✅ Validate AI replies with OpenRouter + Google Docs content

✅ Confirm tag creation and human workflows

✅ Test fallback reply route

✅ Simulate 12-hour tag removal delay (human takeover expiration)

🧾 Your Deliverables

✅ Fully working n8n flow mirroring the Make.com logic

✅ All HTTP request nodes functional and labeled

✅ Google Docs fetch integrated (if opted in)

✅ Clean variable names, commented steps, and reusable layout

✅ Supports Telegram and WhatsApp inbound

✅ Bonus: memory-aware AI prompts and escalation recovery logic

📈 Why This Matters

This is the foundational tech for an AI + CRM resale system. Once stable, we can:

Rapidly clone for new clients

Integrate more AI models or channels (e.g., Messenger)

Add features like funnel detection or CRM sync

Delegate future customizations to junior developers confidently

Let Swen know when you’re ready to port or scale. You’ll get:

API key access

Sample contacts for tests

Valid tag names and escalation flows

Test environment to simulate edge cases

🏗️ System Design Principles

Logic lives in n8n, data stays in Respond.io

Use tags and custom fields for automation state

Visible timeline and status for human agents inside Respond.io

Durable structure that doesn't break if n8n goes down

Modular input/output that adapts per client

This foundation supports advanced use cases like CRM sync, real-time analytics, customer segmentation, and proactive chat outreach.