import * as fs from 'fs';
import * as path from 'path';
import { TabuWorkflowGenerator } from './workflow/tabuWorkflowGenerator';

function main() {
  console.log('🚀 Generating Tabu Enterprise AI Receptionist Workflow...');
  
  try {
    // Create workflow generator
    const generator = new TabuWorkflowGenerator();
    
    // Generate the workflow
    const workflow = generator.generateWorkflow();
    
    // Create output directory if it doesn't exist
    const outputDir = path.join(process.cwd(), 'output');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    // Write the workflow JSON file
    const outputPath = path.join(outputDir, 'tabu-workflow.json');
    fs.writeFileSync(outputPath, JSON.stringify(workflow, null, 2));
    
    console.log('✅ Workflow generated successfully!');
    console.log(`📁 Output file: ${outputPath}`);
    console.log(`📊 Total nodes: ${workflow.nodes.length}`);
    console.log(`🔗 Total connections: ${Object.keys(workflow.connections).length}`);
    
    // Display workflow summary
    console.log('\n📋 Workflow Summary:');
    console.log('==================');
    workflow.nodes.forEach((node, index) => {
      console.log(`${index + 1}. ${node.name} (${node.type})`);
    });
    
    console.log('\n🔧 Next Steps:');
    console.log('==============');
    console.log('1. Update credentials placeholders in the JSON file:');
    console.log('   - YOUR_RESPOND_IO_API_KEY');
    console.log('   - YOUR_GOOGLE_DOC_ID');
    console.log('   - YOUR_TELEGRAM_CHAT_ID');
    console.log('2. Import the JSON file into your n8n Cloud instance');
    console.log('3. Set up credentials in n8n for each service');
    console.log('4. Test the workflow with sample data');
    console.log('5. Activate the workflow when ready');
    
  } catch (error) {
    console.error('❌ Error generating workflow:', error);
    process.exit(1);
  }
}

// Run the generator
if (require.main === module) {
  main();
}
