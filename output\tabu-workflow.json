{"name": "Tabu Enterprise AI Receptionist", "nodes": [{"id": "webhook-trigger", "name": "🔔 Listen to Inbound", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [250, 300], "parameters": {"httpMethod": "POST", "path": "tabu-inbound", "responseMode": "onReceived"}}, {"id": "extract-data", "name": "📋 Extract Data", "type": "n8n-nodes-base.set", "typeVersion": 3, "position": [450, 300], "parameters": {"values": {"values": [{"name": "message_text", "value": "={{$json.body.message.text || $json.message || \"\"}}"}, {"name": "contact_phone", "value": "={{$json.body.contact.phone || $json.phone || \"\"}}"}, {"name": "contact_email", "value": "={{$json.body.contact.email || $json.email || \"\"}}"}, {"name": "channel_type", "value": "={{$json.body.channel.type || \"unknown\"}}"}, {"name": "contact_id", "value": "={{$json.body.contact.id || \"\"}}"}]}, "options": {"dotNotation": true}}}, {"id": "get-contact", "name": "👤 Get Contact", "type": "n8n-nodes-base.httprequest", "typeVersion": 4, "position": [650, 300], "parameters": {"method": "GET", "url": "https://api.respond.io/v2/contacts", "options": {}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "phone", "value": "={{$json.contact_phone}}"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer YOUR_RESPOND_IO_API_KEY"}, {"name": "Content-Type", "value": "application/json"}]}}}, {"id": "route-messages", "name": "🧭 Route Messages", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [850, 300], "parameters": {"mode": "rules", "rules": {"rules": [{"operation": "regex", "value1": "={{$json.message_text}}", "value2": "(human|manager|real person|talk to|someone|agent|representative)", "output": 0}, {"operation": "isEmpty", "value1": "={{$json.message_text}}", "output": 1}]}, "fallbackOutput": 3}}, {"id": "google-docs", "name": "📄 Get Knowledge Base", "type": "n8n-nodes-base.googledocs", "typeVersion": 2, "position": [1050, 200], "parameters": {"operation": "get", "documentId": "YOUR_GOOGLE_DOC_ID"}, "credentials": {"googleApi": "google-service-account"}}, {"id": "prepare-ai-context", "name": "🤖 Prepare AI Context", "type": "n8n-nodes-base.set", "typeVersion": 3, "position": [1250, 200], "parameters": {"values": {"values": [{"name": "restaurant_info", "value": "={{$json.body.content}}"}, {"name": "customer_message", "value": "={{$(\"route-messages\").item.json.message_text}}"}, {"name": "ai_prompt", "value": "You are Tabu Enterprise's AI receptionist. Use this restaurant information: {{$json.restaurant_info}}\n\nCustomer message: {{$json.customer_message}}\n\nRespond professionally and helpfully. If you cannot answer, suggest they speak with a human agent."}]}, "options": {"dotNotation": true}}}, {"id": "ai-agent", "name": "🧠 AI Agent", "type": "n8n-nodes-langchain.agent", "typeVersion": 1, "position": [1450, 200], "parameters": {"sessionId": "={{$(\"get-contact\").item.json.id}}", "chatInput": "={{$json.ai_prompt}}", "options": {"returnIntermediateSteps": false}}}, {"id": "parse-ai-response", "name": "✂️ Parse AI Response", "type": "n8n-nodes-base.set", "typeVersion": 3, "position": [1650, 200], "parameters": {"values": {"values": [{"name": "ai_reply", "value": "={{$json.output}}"}, {"name": "contact_id", "value": "={{$(\"get-contact\").item.json.id}}"}, {"name": "formatted_message", "value": "={{$json.ai_reply}}"}]}, "options": {"dotNotation": true}}}, {"id": "send-ai-reply", "name": "💬 Send AI Reply", "type": "n8n-nodes-base.httprequest", "typeVersion": 4, "position": [1850, 200], "parameters": {"method": "POST", "url": "https://api.respond.io/v2/contacts/{{$json.contact_id}}/messages", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer YOUR_RESPOND_IO_API_KEY"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "jsonParameters": "{\"message\":{\"type\":\"text\",\"text\":\"={{$json.formatted_message}}\"}}"}}, {"id": "add-human-tag", "name": "🏷️ Add Human Tag", "type": "n8n-nodes-base.set", "typeVersion": 3, "position": [1050, 400], "parameters": {"values": {"values": [{"name": "contact_id", "value": "={{$(\"get-contact\").item.json.id}}"}, {"name": "tag_data", "value": {"tags": ["human_needed"]}}]}, "options": {"dotNotation": true}}}, {"id": "tag-contact", "name": "🚨 Tag Contact", "type": "n8n-nodes-base.httprequest", "typeVersion": 4, "position": [1250, 400], "parameters": {"method": "POST", "url": "https://api.respond.io/v2/contacts/{{$json.contact_id}}/tags", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer YOUR_RESPOND_IO_API_KEY"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "jsonParameters": "={{JSON.stringify($json.tag_data)}}"}}, {"id": "telegram-notification", "name": "📞 Telegram Notification", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [1450, 400], "parameters": {"operation": "sendMessage", "chatId": "YOUR_TELEGRAM_CHAT_ID", "text": "🚨 Human escalation requested!\n\nCustomer: {{$(\"get-contact\").item.json.name || \"Unknown\"}}\nPhone: {{$(\"extract-data\").item.json.contact_phone}}\nMessage: {{$(\"extract-data\").item.json.message_text}}\n\nPlease respond in Respond.io platform.", "parseMode": "HTML"}, "credentials": {"telegramApi": "telegram-bot"}}, {"id": "wait-12-hours", "name": "⏰ Wait 12 Hours", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [1650, 400], "parameters": {"amount": 12, "unit": "hours"}}, {"id": "remove-tag", "name": "🔄 Remove Tag", "type": "n8n-nodes-base.httprequest", "typeVersion": 4, "position": [1850, 400], "parameters": {"method": "DELETE", "url": "https://api.respond.io/v2/contacts/{{$(\"tag-contact\").item.json.contact_id}}/tags/human_needed", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer YOUR_RESPOND_IO_API_KEY"}]}}}, {"id": "fallback-message", "name": "🛡️ Fallback Message", "type": "n8n-nodes-base.httprequest", "typeVersion": 4, "position": [1850, 500], "parameters": {"method": "POST", "url": "https://api.respond.io/v2/contacts/{{$(\"get-contact\").item.json.id}}/messages", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer YOUR_RESPOND_IO_API_KEY"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "jsonParameters": "{\"message\":{\"type\":\"text\",\"text\":\"Sorry, I'm experiencing technical difficulties. Please try again later or contact us directly for assistance.\"}}"}}], "connections": {"webhook-trigger": {"main": [[{"node": "extract-data", "type": "main", "index": 0}]]}, "extract-data": {"main": [[{"node": "get-contact", "type": "main", "index": 0}]]}, "get-contact": {"main": [[{"node": "route-messages", "type": "main", "index": 0}]]}, "route-messages": {"main": [[{"node": "add-human-tag", "type": "main", "index": 0}], [{"node": "fallback-message", "type": "main", "index": 0}], [{"node": "google-docs", "type": "main", "index": 0}]]}, "google-docs": {"main": [[{"node": "prepare-ai-context", "type": "main", "index": 0}]]}, "prepare-ai-context": {"main": [[{"node": "ai-agent", "type": "main", "index": 0}]]}, "ai-agent": {"main": [[{"node": "parse-ai-response", "type": "main", "index": 0}]]}, "parse-ai-response": {"main": [[{"node": "send-ai-reply", "type": "main", "index": 0}]]}, "add-human-tag": {"main": [[{"node": "tag-contact", "type": "main", "index": 0}]]}, "tag-contact": {"main": [[{"node": "telegram-notification", "type": "main", "index": 0}]]}, "telegram-notification": {"main": [[{"node": "wait-12-hours", "type": "main", "index": 0}]]}, "wait-12-hours": {"main": [[{"node": "remove-tag", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "saveExecutionProgress": false, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all"}, "staticData": {}, "meta": {"templateCredsSetupCompleted": false}}